package com.ctrip.dcs.tms.transport.interfaces.provider.executor;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.dcs.tms.transport.api.model.OcrValueDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrReqDTO;
import com.ctrip.dcs.tms.transport.application.dto.OcrResultDTO;
import com.ctrip.dcs.tms.transport.application.query.InternationalEntryService;
import com.ctrip.igt.framework.common.result.Result;
import com.ctrip.igt.framework.infrastructure.validator.Validator;
import com.ctrip.igt.framework.soa.server.executor.AbstractRpcExecutor;
import com.ctrip.igt.framework.soa.server.util.ServiceResponseUtils;
import com.ctrip.model.OcrRecognitionRequestType;
import com.ctrip.model.OcrRecognitionResponseType;

@Component
public class OcrRecognitionExecutor extends AbstractRpcExecutor<OcrRecognitionRequestType, OcrRecognitionResponseType> implements Validator<OcrRecognitionRequestType> {

    @Autowired
    private InternationalEntryService internationalEntryService;

    @Override
    public OcrRecognitionResponseType execute(OcrRecognitionRequestType requestType) {
        OcrRecognitionResponseType responseType = new OcrRecognitionResponseType();

        OcrReqDTO ocrReqDTO = new OcrReqDTO();
        ocrReqDTO.setImgUrl(requestType.getImgUrl());
        ocrReqDTO.setImgType(requestType.getType());
        ocrReqDTO.setCityId(requestType.getCityId());

        Result<List<OcrResultDTO>> listResult = internationalEntryService.ocrRecognition(ocrReqDTO);
        if (listResult.isSuccess()) {
            List<OcrResultDTO> data = listResult.getData();
            if (CollectionUtils.isNotEmpty(data)) {
                List<OcrValueDTO> collect = data.stream().map(item -> {
                    OcrValueDTO ocrValueDTO = new OcrValueDTO();
                    ocrValueDTO.setFieldName(item.getFieldName());
                    ocrValueDTO.setValue(item.getValue());
                    ocrValueDTO.setBackfill(item.getBackfill());
                    ocrValueDTO.setBackFillField(item.getBackFillField());
                    ocrValueDTO.setOcrId();
                    return ocrValueDTO;
                }).collect(Collectors.toList());
                responseType.setOcrValueList(collect);

            }

        }
        return ServiceResponseUtils.success(responseType);
    }
}
